import { CandleDataPoint, Trade, Portfolio, Prediction } from '../types';
import { TRADING_FEE_PERCENT, DEEPSEEK_API_KEY, DEEPSEEK_API_URL } from "../constants";

export const getTradingAdvice = async (
  assetSymbol: string,
  candleHistory1m: CandleDataPoint[],
  candleHistory1h: CandleDataPoint[],
  tradeLog: Trade[],
  portfolio: Portfolio
): Promise<Prediction> => {
  if (!DEEPSEEK_API_KEY) {
    throw new Error("DeepSeek API key missing. Configure VITE_DEEPSEEK_API_KEY in your .env.local");
  }

  console.log(`[AI] Getting trading advice for ${assetSymbol}...`);

  const prompt = `
Je bent een crypto trading AI. Analyseer de marktdata en geef een handelsadvies voor ${assetSymbol}.

CONTEXT:
- Portfolio waarde: €${portfolio.totalValue.toFixed(2)}
- P&L: €${portfolio.pl.toFixed(2)} (${portfolio.plPercent.toFixed(2)}%)
- Handelskosten: ${(TRADING_FEE_PERCENT * 100).toFixed(2)}%

MARKTDATA:
1-uur candles (laatste 10):
${candleHistory1h.slice(-10).map(c => `${c.time}: €${c.close.toFixed(4)}`).join('\n')}

1-minuut candles (laatste 20):
${candleHistory1m.slice(-20).map(c => `${c.time}: €${c.close.toFixed(5)}`).join('\n')}

Recente trades:
${tradeLog.length > 0 ? tradeLog.slice(-3).map(t => `${t.type} ${t.amount.toFixed(2)} @ €${t.price.toFixed(4)}`).join('\n') : 'Geen trades'}

Geef alleen een JSON response:
{
  "prediction": "BUY|SELL|HOLD",
  "confidence": 0.0,
  "reasoning": "Korte uitleg van je analyse",
  "strategyAdjustment": "Eventuele aanpassingen",
  "newsSummary": "Geen nieuws beschikbaar",
  "positionSizing": {
    "pctOfPortfolio": 10
  }
}
`;

  try {
    const response = await fetch(DEEPSEEK_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [{ role: 'user', content: prompt }],
        temperature: 0.3
      })
    });

    if (!response.ok) {
      const errorBody = await response.text();
      throw new Error(`DeepSeek API Error: ${response.status} ${response.statusText} - ${errorBody}`);
    }

    const data = await response.json();
    const jsonText = data.choices[0]?.message?.content?.trim().replace(/```json\n?|\n?```/g, '') ?? '{}';
    console.log(`[AI] Raw response for ${assetSymbol}:`, jsonText);
    
    const parsedResponse = JSON.parse(jsonText);

    if (
      typeof parsedResponse.prediction !== 'string' ||
      !['BUY', 'SELL', 'HOLD'].includes(parsedResponse.prediction)
    ) {
      throw new Error("DeepSeek response is malformed or missing prediction.");
    }

    return parsedResponse as Prediction;
  } catch (error) {
    console.error(`[AI] Error getting advice for ${assetSymbol}:`, error);
    throw error;
  }
};
