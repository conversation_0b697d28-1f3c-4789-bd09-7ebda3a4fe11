import { BanditArmState, BanditState, StrategyVariant } from '../types';

// Marsaglia & Tsang method for Gamma(shape=k, scale=theta)
function randomGamma(shape: number, scale = 1): number {
  if (shape <= 0) return 0;

  if (shape < 1) {
    // <PERSON>'s transformation: Gamma(a) = Gamma(a+1) * U^(1/a)
    const u = Math.random();
    return randomGamma(shape + 1, scale) * Math.pow(u, 1 / shape);
  }

  const d = shape - 1 / 3;
  const c = 1 / Math.sqrt(9 * d);
  while (true) {
    let x: number, v: number;
    do {
      // Standard normal via Box-Muller
      const u1 = Math.random();
      const u2 = Math.random();
      const z = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);
      x = z;
      v = 1 + c * x;
    } while (v <= 0);

    v = v * v * v;
    const u = Math.random();
    if (u < 1 - 0.0331 * (x * x) * (x * x)) return d * v * scale;
    if (Math.log(u) < 0.5 * x * x + d * (1 - v + Math.log(v))) return d * v * scale;
  }
}

function randomBeta(alpha: number, beta: number): number {
  const x = randomGamma(alpha, 1);
  const y = randomGamma(beta, 1);
  return x / (x + y);
}

export function initBanditState(variants: StrategyVariant[], priorAlpha: number, priorBeta: number): BanditState {
  const arms: BanditArmState[] = variants.map(v => ({
    id: v.id,
    alpha: priorAlpha,
    beta: priorBeta,
    pulls: 0,
    wins: 0,
    losses: 0,
  }));

  const selectionCountById: Record<string, number> = {};
  variants.forEach(v => { selectionCountById[v.id] = 0; });

  return {
    arms,
    selectionCountById,
    lastUpdateAt: Date.now(),
  };
}

export function thompsonSampleStrategy(state: BanditState): { id: string; draw: number } {
  if (!state.arms.length) throw new Error('No bandit arms available');
  let bestId = state.arms[0].id;
  let bestDraw = -1;

  for (const arm of state.arms) {
    const draw = randomBeta(Math.max(arm.alpha, 1e-6), Math.max(arm.beta, 1e-6));
    if (draw > bestDraw) {
      bestDraw = draw;
      bestId = arm.id;
    }
  }
  return { id: bestId, draw: bestDraw };
}

export function selectNextStrategy(state: BanditState): BanditState {
  const { id } = thompsonSampleStrategy(state);
  const selectionCountById = { ...state.selectionCountById, [id]: (state.selectionCountById[id] ?? 0) + 1 };
  const arms = state.arms.map(a => (a.id === id ? { ...a, lastSelectedAt: Date.now() } : a));
  return {
    ...state,
    arms,
    selectedStrategyId: id,
    selectionCountById,
  };
}

// successWeight: 0..1 (can be advisor-weighted)
// Returns a new state with the updated arm
export function updateBanditWithReward(state: BanditState, strategyId: string, successWeight: number): BanditState {
  const arms = state.arms.map(arm => {
    if (arm.id !== strategyId) return arm;
    const w = Math.min(Math.max(successWeight, 0), 1);
    return {
      ...arm,
      alpha: arm.alpha + w,
      beta: arm.beta + (1 - w),
      wins: arm.wins + w,
      losses: arm.losses + (1 - w),
      pulls: arm.pulls + 1,
    };
  });
  return { ...state, arms, lastUpdateAt: Date.now() };
}

export function expectedValue(alpha: number, beta: number): number {
  const s = alpha + beta;
  if (s <= 0) return 0.5;
  return alpha / s;
}