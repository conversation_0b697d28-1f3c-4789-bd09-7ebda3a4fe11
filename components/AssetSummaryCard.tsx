import React from 'react';
import Sparkline from './Sparkline';
import { CandleDataPoint, Prediction, AppState } from '../types';

interface AssetSummaryCardProps {
  symbol: string;
  price: number;
  candleHistory: CandleDataPoint[];
  prediction: Prediction | null;
  status: AppState;
  error?: string | null;
  isDataSufficient: boolean;
  lastAdviceAt?: number;
}

const StatusChip: React.FC<{ status: AppState; isDataSufficient: boolean }> = ({ status, isDataSufficient }) => {
  if (!isDataSufficient && status !== AppState.Analyzing && status !== AppState.Error) {
    return (
      <span className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-medium bg-yellow-500/15 text-yellow-400">
        • Data
      </span>
    );
  }
  switch (status) {
    case AppState.Analyzing:
      return (
        <span className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-medium bg-teal-500/15 text-teal-400">
          • Analyse
        </span>
      );
    case AppState.Error:
      return (
        <span className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-medium bg-red-500/15 text-red-400">
          • Fout
        </span>
      );
    case AppState.Monitoring:
      return (
        <span className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-medium bg-blue-500/15 text-blue-400">
          • Monitor
        </span>
      );
    default:
      return (
        <span className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-medium bg-gray-500/15 text-gray-300">
          • Wacht
        </span>
      );
  }
};

const AdviceChip: React.FC<{ prediction: Prediction | null }> = ({ prediction }) => {
  if (!prediction) return null;
  const map = {
    BUY: { label: 'KOPEN', color: 'bg-green-500/15 text-green-400' },
    SELL: { label: 'VERKOPEN', color: 'bg-red-500/15 text-red-400' },
    HOLD: { label: 'AFWACHTEN', color: 'bg-yellow-500/15 text-yellow-400' },
  } as const;
  const p = map[prediction.prediction];
  return (
    <span className={`inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-semibold ${p.color}`}>
      {p.label}
    </span>
  );
};


const AssetSummaryCard: React.FC<AssetSummaryCardProps> = ({
  symbol,
  price,
  candleHistory,
  prediction,
  status,
  error,
  isDataSufficient,
  lastAdviceAt,
}) => {
  const display = symbol.replace('-EUR', '');
  const pricePrecision = price > 1 ? 2 : 5;

  const first = candleHistory?.[0]?.close ?? 0;
  const last = candleHistory?.[candleHistory.length - 1]?.close ?? 0;
  const changePct = first > 0 ? ((last - first) / first) * 100 : 0;
  const changeColor = changePct >= 0 ? 'text-green-400' : 'text-red-400';
  const changeSign = changePct >= 0 ? '+' : '';

  return (
    <div className="bg-gradient-to-br from-gray-800/70 to-gray-900/70 rounded-2xl p-5 hover:from-gray-800/80 hover:to-gray-900/80 transition-all duration-300 border border-gray-700/30 backdrop-blur-sm">
      <div className="flex items-start justify-between mb-4">
        <div>
          <div className="text-white font-bold text-xl tracking-tight mb-1">{display}</div>
          <div className="flex items-center gap-2">
            <StatusChip status={status} isDataSufficient={isDataSufficient} />
            <AdviceChip prediction={prediction} />
          </div>
        </div>
        {typeof lastAdviceAt === 'number' && (
          <div className="text-right">
            <div className="text-[10px] text-gray-500">Laatste advies</div>
            <div className="text-[11px] text-gray-300 font-mono">{new Date(lastAdviceAt).toLocaleTimeString()}</div>
          </div>
        )}
      </div>

      <div className="flex items-end justify-between mb-4">
        <div>
          <div className="text-3xl font-extrabold text-white leading-none mb-1">€{price.toFixed(pricePrecision)}</div>
          <div className={`text-sm font-semibold flex items-center gap-1 ${changeColor}`}>
            {changePct >= 0 ? '↗' : '↘'} {changeSign}{changePct.toFixed(2)}%
          </div>
        </div>
        <div className="w-32 md:w-40">
          <Sparkline
            data={candleHistory}
            height={52}
            stroke={changePct >= 0 ? '#34d399' : '#f87171'}
            gradientId={`spark_${display}`}
          />
        </div>
      </div>

      {error ? (
        <div className="text-[11px] text-red-400/80 truncate">{error}</div>
      ) : (
        prediction ? (
          <div className="text-[11px] text-gray-400 truncate">
            Zekerheid: <span className="text-gray-300 font-semibold">{(prediction.confidence * 100).toFixed(0)}%</span> •{' '}
            <span className="italic">{prediction.strategyAdjustment}</span>
          </div>
        ) : (
          <div className="text-[11px] text-gray-500">Wachten op AI-signaal...</div>
        )
      )}
    </div>
  );
};

export default AssetSummaryCard;