import React, { useState, useEffect } from 'react';
import { Database, Wifi, AlertCircle, CheckCircle, Clock } from 'lucide-react';

interface DataStatusDebugProps {
  candleHistories1m: Record<string, any[]>;
  candleHistories1h: Record<string, any[]>;
  assets: any[];
  predictions: Record<string, any>;
}

const DataStatusDebug: React.FC<DataStatusDebugProps> = ({
  candleHistories1m,
  candleHistories1h,
  assets,
  predictions
}) => {
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [updateCount, setUpdateCount] = useState(0);

  // Monitor for data changes
  useEffect(() => {
    setLastUpdate(new Date());
    setUpdateCount(prev => prev + 1);
  }, [candleHistories1m, candleHistories1h, assets, predictions]);

  const symbols = ['XRP-EUR', 'SOL-EUR', 'DOGE-EUR'];

  return (
    <div className="bg-gray-800/60 rounded-xl p-4 border border-gray-700/30">
      <div className="flex items-center gap-2 mb-4">
        <Database size={18} className="text-blue-400" />
        <span className="text-sm font-semibold text-gray-200">Data Status Debug</span>
        <div className="flex items-center gap-1 text-xs text-gray-400">
          <Clock size={12} />
          <span>Updates: {updateCount}</span>
          {lastUpdate && <span>• Last: {lastUpdate.toLocaleTimeString()}</span>}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {symbols.map(symbol => {
          const candles1m = candleHistories1m[symbol] || [];
          const candles1h = candleHistories1h[symbol] || [];
          const asset = assets.find(a => a.symbol === symbol);
          const prediction = predictions[symbol];
          
          const hasData = candles1m.length > 0 || candles1h.length > 0;
          const hasPrice = asset && asset.price > 0;
          const hasPrediction = !!prediction;
          const isReady = candles1m.length >= 10 && candles1h.length >= 3;

          return (
            <div key={symbol} className="bg-gray-900/40 rounded-lg p-3 border border-gray-700/30">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-semibold text-white">{symbol}</span>
                <div className="flex items-center gap-1">
                  {hasData ? (
                    <CheckCircle size={12} className="text-green-400" />
                  ) : (
                    <AlertCircle size={12} className="text-red-400" />
                  )}
                  {hasPrice ? (
                    <Wifi size={12} className="text-green-400" />
                  ) : (
                    <Wifi size={12} className="text-red-400" />
                  )}
                </div>
              </div>

              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-400">1m Candles:</span>
                  <span className={candles1m.length >= 10 ? 'text-green-400' : 'text-yellow-400'}>
                    {candles1m.length}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-400">1h Candles:</span>
                  <span className={candles1h.length >= 3 ? 'text-green-400' : 'text-yellow-400'}>
                    {candles1h.length}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-400">Price:</span>
                  <span className={hasPrice ? 'text-green-400' : 'text-red-400'}>
                    {hasPrice ? `€${asset.price.toFixed(4)}` : 'No price'}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-400">Prediction:</span>
                  <span className={hasPrediction ? 'text-green-400' : 'text-gray-500'}>
                    {hasPrediction ? prediction.prediction : 'None'}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-400">Ready:</span>
                  <span className={isReady ? 'text-green-400' : 'text-red-400'}>
                    {isReady ? 'Yes' : 'No'}
                  </span>
                </div>
              </div>

              {candles1m.length > 0 && (
                <div className="mt-2 pt-2 border-t border-gray-700/30">
                  <div className="text-xs text-gray-400">Latest 1m:</div>
                  <div className="text-xs text-gray-300 font-mono">
                    {candles1m[candles1m.length - 1]?.time || 'No time'} - 
                    €{candles1m[candles1m.length - 1]?.close?.toFixed(4) || 'No price'}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-700/30">
        <div className="text-xs text-gray-400 mb-2">Raw Data Counts:</div>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <div className="text-gray-400">Candle Histories 1m:</div>
            <div className="text-gray-300 font-mono">
              {JSON.stringify(Object.fromEntries(
                Object.entries(candleHistories1m).map(([k, v]) => [k, Array.isArray(v) ? v.length : 'invalid'])
              ), null, 2)}
            </div>
          </div>
          <div>
            <div className="text-gray-400">Candle Histories 1h:</div>
            <div className="text-gray-300 font-mono">
              {JSON.stringify(Object.fromEntries(
                Object.entries(candleHistories1h).map(([k, v]) => [k, Array.isArray(v) ? v.length : 'invalid'])
              ), null, 2)}
            </div>
          </div>
        </div>
      </div>

      <div className="mt-4 pt-4 border-t border-gray-700/30">
        <div className="text-xs text-gray-400 mb-2">Assets Array:</div>
        <div className="text-xs text-gray-300 font-mono">
          {JSON.stringify(assets.map(a => ({ symbol: a.symbol, price: a.price })), null, 2)}
        </div>
      </div>
    </div>
  );
};

export default DataStatusDebug;
