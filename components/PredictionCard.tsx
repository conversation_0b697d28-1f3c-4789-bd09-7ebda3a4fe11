
import React from 'react';
import { Prediction } from '../types';
import { TrendingUp, TrendingDown, PauseCircle, BrainCircuit, AlertTriangle, Zap, Newspaper, Activity, Shield, Bell, Gauge, Landmark, ListChecks, Clock, Compass } from 'lucide-react';

interface PredictionCardProps {
  prediction: Prediction | null;
  isLoading: boolean;
  error: string | null;
}

const PredictionCard: React.FC<PredictionCardProps> = ({ prediction, isLoading, error }) => {
  const getPredictionStyling = (p: 'BUY' | 'SELL' | 'HOLD') => {
    switch (p) {
      case 'BUY':
        return {
          icon: <TrendingUp size={28} />,
          bgColor: 'bg-green-500/10',
          textColor: 'text-green-400',
          borderColor: 'border-green-500/50',
          text: 'KOPEN',
        };
      case 'SELL':
        return {
          icon: <TrendingDown size={28} />,
          bgColor: 'bg-red-500/10',
          textColor: 'text-red-400',
          borderColor: 'border-red-500/50',
          text: 'VERKOPEN',
        };
      case 'HOLD':
        return {
          icon: <PauseCircle size={28} />,
          bgColor: 'bg-yellow-500/10',
          textColor: 'text-yellow-400',
          borderColor: 'border-yellow-500/50',
          text: 'AFWACHTEN',
        };
    }
  };

  const renderContent = () => {
    if (isLoading) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-gray-400 text-center">
          <BrainCircuit className="h-12 w-12 animate-pulse text-teal-500" />
          <p className="mt-2 text-md font-semibold">AI analyseert...</p>
          <p className="text-xs">Grafiek & nieuws worden onderzocht.</p>
        </div>
      );
    }

    if (error) {
       return (
        <div className="flex flex-col items-center justify-center h-full text-red-400 text-center">
          <AlertTriangle className="h-12 w-12" />
          <p className="mt-2 text-md font-semibold">Analyse Fout</p>
          <p className="text-xs px-2">{error}</p>
        </div>
      );
    }
    
    if (!prediction) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-gray-500 text-center">
            <Zap className="h-12 w-12" />
            <p className="mt-2 text-md font-semibold">Wachten op analyse</p>
            <p className="text-xs">AI wacht op voldoende data.</p>
        </div>
      );
    }

    const styling = getPredictionStyling(prediction.prediction);
    
    return (
      <div className="space-y-4">
        <div className={`flex items-center justify-between rounded-lg p-4 ${styling.bgColor}`}>
          <div className="flex items-center gap-3">
            <div className={styling.textColor}>{styling.icon}</div>
            <div>
              <div className={`text-xl md:text-2xl font-extrabold ${styling.textColor}`}>{styling.text}</div>
              <div className="text-[11px] text-gray-400">AI-advies</div>
            </div>
          </div>
          <div className="w-40 md:w-56">
            <div className="text-xs text-gray-400 mb-1 text-right">Zekerheid</div>
            <div className="w-full bg-gray-700 rounded-full h-2">
              <div
                className={`${styling.textColor.replace('text-', 'bg-')} h-2 rounded-full`}
                style={{ width: `${prediction.confidence * 100}%` }}
              />
            </div>
            <div className="text-[11px] text-gray-400 mt-1 text-right">{(prediction.confidence * 100).toFixed(0)}%</div>
          </div>
        </div>

        {(prediction.regime || prediction.horizon) && (
          <div className="flex flex-wrap gap-2">
            {prediction.regime && (
              <span className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-medium bg-blue-500/15 text-blue-300">
                <Compass size={12} /> Regime: {prediction.regime.replaceAll('_', ' ')}
              </span>
            )}
            {prediction.horizon && (
              <span className="inline-flex items-center gap-1 rounded-full px-2 py-0.5 text-[10px] font-medium bg-purple-500/15 text-purple-300">
                <Clock size={12} /> Horizon: {prediction.horizon === 'short_term' ? 'Kort' : 'Lang'}
              </span>
            )}
          </div>
        )}
    
        <details className="rounded-lg bg-gray-800/40 p-4 border border-gray-700/50">
          <summary className="cursor-pointer select-none text-sm text-gray-300 font-semibold">Toelichting</summary>
          <div className="mt-3 space-y-4 text-xs text-gray-400">
            <div>
              <span className="text-gray-300 font-semibold">Redenering:</span> <span className="italic">"{prediction.reasoning}"</span>
            </div>
            <div>
              <span className="text-gray-300 font-semibold">Strategie Aanpassing:</span> <span className="italic">"{prediction.strategyAdjustment}"</span>
            </div>

            {prediction.signalsUsed && prediction.signalsUsed.length > 0 && (
              <div className="pt-2 border-t border-gray-700/50">
                <div className="text-gray-300 font-semibold flex items-center gap-2"><Activity size={14} />Gebruikte signalen</div>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  {prediction.signalsUsed.map((s, i) => (
                    <li key={i}>
                      <span className="text-gray-300">{s.name}</span>
                      {s.why && <span> — {s.why}</span>}
                      {typeof s.strength === 'number' && <span> • sterkte {(s.strength * 100).toFixed(0)}%</span>}
                      {s.notes && <span> • {s.notes}</span>}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {prediction.executionPlan && (
              <div className="pt-2 border-t border-gray-700/50">
                <div className="text-gray-300 font-semibold flex items-center gap-2"><Landmark size={14} />Executie</div>
                <div className="mt-1 grid grid-cols-2 gap-2">
                  {prediction.executionPlan.style && <div className="text-gray-300">Stijl: <span className="text-gray-400">{prediction.executionPlan.style}</span></div>}
                  {prediction.executionPlan.urgency && <div className="text-gray-300">Urgentie: <span className="text-gray-400">{prediction.executionPlan.urgency}</span></div>}
                  {typeof prediction.executionPlan.maxSlippageBps === 'number' && <div className="text-gray-300">Max slippage: <span className="text-gray-400">{prediction.executionPlan.maxSlippageBps} bps</span></div>}
                  {typeof prediction.executionPlan.participationRatePct === 'number' && <div className="text-gray-300">Participatie: <span className="text-gray-400">{prediction.executionPlan.participationRatePct}%</span></div>}
                </div>
                {prediction.executionPlan.notes && <div className="mt-1 italic">{prediction.executionPlan.notes}</div>}
              </div>
            )}

            {prediction.positionSizing && (
              <div className="pt-2 border-t border-gray-700/50">
                <div className="text-gray-300 font-semibold flex items-center gap-2"><Gauge size={14} />Positiegrootte & Risk</div>
                <div className="mt-1 grid grid-cols-2 gap-2">
                  {typeof prediction.positionSizing.pctOfPortfolio === 'number' && <div className="text-gray-300">Grootte: <span className="text-gray-400">{prediction.positionSizing.pctOfPortfolio}%</span></div>}
                  {prediction.positionSizing.stopLoss && (
                    <div className="text-gray-300">Stop: <span className="text-gray-400">{prediction.positionSizing.stopLoss.type} {Math.round((prediction.positionSizing.stopLoss.value ?? 0)*10000)/100}%</span></div>
                  )}
                  {prediction.positionSizing.takeProfit && (
                    <div className="text-gray-300">Take Profit: <span className="text-gray-400">{prediction.positionSizing.takeProfit.rr ? `RR ${prediction.positionSizing.takeProfit.rr}` : ''} {prediction.positionSizing.takeProfit.targetPrice ? `@ €${prediction.positionSizing.takeProfit.targetPrice}` : ''}</span></div>
                  )}
                </div>
                {prediction.positionSizing.rationale && <div className="mt-1 italic">{prediction.positionSizing.rationale}</div>}
                {prediction.positionSizing.hedge && <div className="mt-1">Hedge: <span className="italic">{prediction.positionSizing.hedge}</span></div>}
              </div>
            )}

            {prediction.monitoring && (
              <div className="pt-2 border-t border-gray-700/50">
                <div className="text-gray-300 font-semibold flex items-center gap-2"><Bell size={14} />Monitoring</div>
                {prediction.monitoring.alerts && prediction.monitoring.alerts.length > 0 && (
                  <div className="mt-1">
                    <div className="text-gray-500 mb-1">Alerts:</div>
                    <ul className="list-disc list-inside space-y-1">
                      {prediction.monitoring.alerts.map((a, i) => <li key={i}>{a}</li>)}
                    </ul>
                  </div>
                )}
                {prediction.monitoring.kpis && prediction.monitoring.kpis.length > 0 && (
                  <div className="mt-2">
                    <div className="text-gray-500 mb-1">KPI’s:</div>
                    <ul className="list-disc list-inside space-y-1">
                      {prediction.monitoring.kpis.map((k, i) => <li key={i}>{k}</li>)}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {prediction.governance && (
              <div className="pt-2 border-t border-gray-700/50">
                <div className="text-gray-300 font-semibold flex items-center gap-2"><Shield size={14} />Governance & Compliance</div>
                {prediction.governance.riskLimits && prediction.governance.riskLimits.length > 0 && (
                  <div className="mt-1">
                    <div className="text-gray-500 mb-1">Risk Limits:</div>
                    <ul className="list-disc list-inside space-y-1">
                      {prediction.governance.riskLimits.map((r, i) => <li key={i}>{r}</li>)}
                    </ul>
                  </div>
                )}
                {prediction.governance.complianceNotes && prediction.governance.complianceNotes.length > 0 && (
                  <div className="mt-2">
                    <div className="text-gray-500 mb-1">Compliance:</div>
                    <ul className="list-disc list-inside space-y-1">
                      {prediction.governance.complianceNotes.map((c, i) => <li key={i}>{c}</li>)}
                    </ul>
                  </div>
                )}
              </div>
            )}

            {prediction.dataSummary && prediction.dataSummary.length > 0 && (
              <div className="pt-2 border-t border-gray-700/50">
                <div className="text-gray-300 font-semibold flex items-center gap-2"><ListChecks size={14} />Data-overzicht</div>
                <ul className="list-disc list-inside mt-1 space-y-1">
                  {prediction.dataSummary.map((d, i) => <li key={i}>{d}</li>)}
                </ul>
              </div>
            )}

            {prediction.xrpNotes && (
              <div className="pt-2 border-t border-gray-700/50">
                <div className="text-gray-300 font-semibold">XRP-notities</div>
                <p className="italic mt-1">{prediction.xrpNotes}</p>
              </div>
            )}

            {prediction.newsSummary && (
              <div className="pt-2 border-t border-gray-700/50">
                <div className="text-gray-300 font-semibold flex items-center gap-2"><Newspaper size={14} />Nieuwsanalyse</div>
                <p className="italic">"{prediction.newsSummary}"</p>
                {prediction.sources && prediction.sources.length > 0 && (
                  <div className="mt-1 text-xs">
                    <span className="text-gray-500">Bronnen:</span>
                    <ul className="list-disc list-inside space-y-1">
                      {prediction.sources.map((source, index) => (
                        <li key={index} className="truncate">
                          <a
                            href={source.uri}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-teal-400 hover:text-teal-300 hover:underline text-[11px]"
                          >
                            {source.title}
                          </a>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            )}
          </div>
        </details>
      </div>
    );
  };

  return (
    <div className="bg-gray-800/60 p-4 md:p-5 rounded-lg min-h-[160px]">
      <h3 className="text-xl md:text-2xl font-bold text-white mb-4">AI Handelsadvies</h3>
      <div className="p-0">{renderContent()}</div>
    </div>
  );
};

export default PredictionCard;