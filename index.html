
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Crypto Trading AI Simulator</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
      /* Custom scrollbar for a more modern look */
      ::-webkit-scrollbar {
        width: 8px;
      }
      ::-webkit-scrollbar-track {
        background: #1a202c; /* bg-gray-800 */
      }
      ::-webkit-scrollbar-thumb {
        background: #4a5568; /* bg-gray-600 */
        border-radius: 4px;
      }
      ::-webkit-scrollbar-thumb:hover {
        background: #718096; /* bg-gray-500 */
      }
    </style>
  <script type="importmap">
{
  "imports": {

    "react/": "https://esm.sh/react@^19.1.1/",
    "react": "https://esm.sh/react@^19.1.1",
    "react-dom/": "https://esm.sh/react-dom@^19.1.1/",
    "recharts": "https://esm.sh/recharts@^3.1.2",
    "lucide-react": "https://esm.sh/lucide-react@^0.537.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
  <body class="bg-gray-900 text-gray-100">
    <div id="root"></div>
    <script type="module" src="/index.tsx"></script>
  </body>
</html>