import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { CandleDataPoint } from '../types';

interface PriceChartProps {
  data: CandleDataPoint[];
  assetSymbol: string;
}

const CustomTooltip: React.FC<any> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const data: CandleDataPoint = payload[0].payload;
    const pricePrecision = data.close > 1 ? 2 : 5;
    return (
      <div className="bg-gray-700/80 backdrop-blur-sm p-3 rounded-lg border border-gray-600 shadow-lg text-xs font-mono">
        <p className="label text-sm text-gray-200 font-bold mb-2">{`Tijd: ${label}`}</p>
        <p className="text-gray-300">Open: <span className="font-semibold">€{data.open.toFixed(pricePrecision)}</span></p>
        <p className="text-green-400">High: <span className="font-semibold">€{data.high.toFixed(pricePrecision)}</span></p>
        <p className="text-red-400">Low:  <span className="font-semibold">€{data.low.toFixed(pricePrecision)}</span></p>
        <p className="text-teal-300">Close:<span className="font-semibold">€{data.close.toFixed(pricePrecision)}</span></p>
        <p className="text-gray-300 mt-1">Vol:  <span className="font-semibold">{data.volume.toFixed(2)}</span></p>
      </div>
    );
  }
  return null;
};

const PriceChart: React.FC<PriceChartProps> = ({ data, assetSymbol }) => {
  if (data.length === 0) {
    return (
        <div className="h-80 flex items-center justify-center text-gray-500">
            Live grafiekdata voor {assetSymbol} wordt geladen...
        </div>
    );
  }
  
  const formatYAxisTick = (value: number) => {
    const abs = Math.abs(Number(value));
    if (abs >= 1000) {
      return new Intl.NumberFormat('nl-NL', { notation: 'compact', maximumFractionDigits: 2 }).format(Number(value));
    }
    if (abs >= 1) {
      return Number(value).toFixed(2);
    }
    return Number(value).toFixed(4);
  };

  return (
    <div className="h-72 md:h-80 lg:h-96 w-full">
      <ResponsiveContainer>
        <LineChart data={data} margin={{ top: 5, right: 20, left: -10, bottom: 5 }}>
          <defs>
            <linearGradient id="colorPrice" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#2dd4bf" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#2dd4bf" stopOpacity={0}/>
            </linearGradient>
          </defs>
          <CartesianGrid strokeDasharray="2 4" stroke="rgba(75,85,99,0.3)" vertical={false} />
          <XAxis
            dataKey="time"
            stroke="#9ca3af"
            fontSize={12}
            tick={{ fill: '#9ca3af' }}
            tickLine={false}
            axisLine={false}
            interval="preserveStartEnd"
            minTickGap={80}
          />
          <YAxis
            domain={['dataMin', 'dataMax']}
            stroke="#9ca3af"
            fontSize={12}
            tick={{ fill: '#9ca3af' }}
            tickLine={false}
            axisLine={false}
            tickFormatter={formatYAxisTick}
            tickCount={5}
            tickMargin={8}
            width={60}
            orientation="right"
          />
          <Tooltip content={<CustomTooltip />} />
          <Line 
            type="monotone" 
            dataKey="close" 
            stroke="#2dd4bf" 
            strokeWidth={2}
            dot={false}
            activeDot={{ r: 6, fill: '#2dd4bf', stroke: '#fff', strokeWidth: 2 }} 
            fillOpacity={1} 
            fill="url(#colorPrice)" 
          />
        </LineChart>
      </ResponsiveContainer>
    </div>
  );
};

export default PriceChart;
