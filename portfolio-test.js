// Portfolio Balance Test Script
// Run this in the browser console to verify portfolio calculations

console.log('🧪 Portfolio Balance Test');

// Test function to verify portfolio consistency
window.testPortfolioBalance = function() {
  console.log('\n📊 Portfolio Balance Verification');
  
  const portfolio = JSON.parse(localStorage.getItem('cts_portfolio_v1') || '{}');
  const trades = JSON.parse(localStorage.getItem('cts_trades_v1') || '[]');
  
  console.log('Current Portfolio:', portfolio);
  console.log('Total Trades:', trades.length);
  
  // Calculate expected balance from trades
  let expectedBalance = 500; // Starting balance
  let totalFees = 0;
  let totalBought = 0;
  let totalSold = 0;
  
  trades.forEach((trade, index) => {
    console.log(`Trade ${index + 1}: ${trade.type} ${trade.amount.toFixed(6)} ${trade.symbol} @ €${trade.price.toFixed(4)}`);
    console.log(`  Total: €${trade.total.toFixed(2)}, Fee: €${trade.fee.toFixed(3)}`);
    
    totalFees += trade.fee;
    
    if (trade.type === 'BUY') {
      const cost = trade.total + trade.fee;
      expectedBalance -= cost;
      totalBought += cost;
      console.log(`  Balance after BUY: €${expectedBalance.toFixed(2)} (spent €${cost.toFixed(2)})`);
    } else if (trade.type === 'SELL') {
      const received = trade.total - trade.fee;
      expectedBalance += received;
      totalSold += received;
      console.log(`  Balance after SELL: €${expectedBalance.toFixed(2)} (received €${received.toFixed(2)})`);
    }
  });
  
  console.log('\n📈 Summary:');
  console.log(`Expected Balance: €${expectedBalance.toFixed(2)}`);
  console.log(`Actual Balance: €${portfolio.balance?.toFixed(2) || 'N/A'}`);
  console.log(`Total Fees Paid: €${totalFees.toFixed(3)}`);
  console.log(`Total Bought: €${totalBought.toFixed(2)}`);
  console.log(`Total Sold: €${totalSold.toFixed(2)}`);
  console.log(`Net Trading: €${(totalSold - totalBought).toFixed(2)}`);
  
  // Check if balance is correct
  const balanceCorrect = Math.abs(expectedBalance - (portfolio.balance || 0)) < 0.01;
  console.log(`Balance Correct: ${balanceCorrect ? '✅' : '❌'}`);
  
  // Check if we're within the 500 euro limit
  const withinLimit = (portfolio.balance || 0) <= 500.01; // Small tolerance for rounding
  console.log(`Within 500€ Limit: ${withinLimit ? '✅' : '❌'}`);
  
  // Check total portfolio value
  const totalValue = portfolio.totalValue || 0;
  console.log(`Total Portfolio Value: €${totalValue.toFixed(2)}`);
  
  if (!balanceCorrect) {
    console.error('❌ Balance mismatch detected!');
    console.error(`Difference: €${Math.abs(expectedBalance - (portfolio.balance || 0)).toFixed(3)}`);
  }
  
  if (!withinLimit) {
    console.error('❌ Portfolio exceeds 500€ limit!');
    console.error(`Excess: €${((portfolio.balance || 0) - 500).toFixed(2)}`);
  }
  
  return {
    expectedBalance,
    actualBalance: portfolio.balance,
    totalFees,
    balanceCorrect,
    withinLimit,
    totalValue
  };
};

// Function to reset portfolio if needed
window.resetPortfolio = function() {
  if (confirm('Are you sure you want to reset the portfolio to 500€?')) {
    localStorage.removeItem('cts_portfolio_v1');
    localStorage.removeItem('cts_trades_v1');
    console.log('Portfolio reset! Refresh the page.');
  }
};

// Function to monitor trades in real-time
window.monitorTrades = function() {
  console.log('👀 Starting trade monitoring...');
  
  let lastTradeCount = 0;
  
  const monitor = setInterval(() => {
    const trades = JSON.parse(localStorage.getItem('cts_trades_v1') || '[]');
    
    if (trades.length > lastTradeCount) {
      console.log(`\n🔔 New trade detected! Running balance check...`);
      testPortfolioBalance();
      lastTradeCount = trades.length;
    }
  }, 1000);
  
  console.log('Trade monitoring started. Use stopMonitoring() to stop.');
  window.tradeMonitorInterval = monitor;
};

window.stopMonitoring = function() {
  if (window.tradeMonitorInterval) {
    clearInterval(window.tradeMonitorInterval);
    console.log('Trade monitoring stopped.');
  }
};

console.log('\n✅ Portfolio test functions loaded:');
console.log('- testPortfolioBalance(): Check portfolio consistency');
console.log('- resetPortfolio(): Reset portfolio to 500€');
console.log('- monitorTrades(): Monitor trades in real-time');
console.log('- stopMonitoring(): Stop monitoring');

// Run initial test
testPortfolioBalance();
