import React from 'react';
import { ResponsiveContainer, AreaChart, Area } from 'recharts';
import { CandleDataPoint } from '../types';

interface SparklineProps {
  data: CandleDataPoint[];
  height?: number;
  stroke?: string;
  gradientId?: string;
  className?: string;
}

const Sparkline: React.FC<SparklineProps> = ({
  data,
  height = 56,
  stroke = '#2dd4bf',
  gradientId = 'sparklineGradient',
  className = '',
}) => {
  const processed = data.map(d => ({ close: d.close }));

  return (
    <div className={className} style={{ height }}>
      <ResponsiveContainer width="100%" height="100%">
        <AreaChart data={processed} margin={{ top: 0, right: 0, bottom: 0, left: 0 }}>
          <defs>
            <linearGradient id={gradientId} x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor={stroke} stopOpacity={0.6} />
              <stop offset="100%" stopColor={stroke} stopOpacity={0} />
            </linearGradient>
          </defs>
          <Area
            type="monotone"
            dataKey="close"
            stroke={stroke}
            strokeWidth={2}
            fillOpacity={1}
            fill={`url(#${gradientId})`}
            isAnimationActive={false}
          />
        </AreaChart>
      </ResponsiveContainer>
    </div>
  );
};

export default Sparkline;