
import React from 'react';
import { BrainCircuit } from 'lucide-react';

const Header: React.FC = () => {
  return (
    <header className="bg-gradient-to-r from-gray-800/80 to-gray-900/80 backdrop-blur-sm sticky top-0 z-10 border-b border-gray-700/30">
      <div className="container mx-auto px-4 lg:px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="p-2 bg-teal-500/10 rounded-xl border border-teal-500/20">
              <BrainCircuit className="text-teal-400 h-7 w-7" />
            </div>
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-white tracking-tight">
                Crypto Trading <span className="text-teal-400">AI</span>
              </h1>
              <p className="text-sm text-gray-400 mt-0.5">Autonomous Trading Simulator</p>
            </div>
          </div>
          <div className="hidden md:flex items-center gap-4">
            <div className="text-right">
              <div className="text-xs text-gray-400">Powered by</div>
              <div className="text-sm font-semibold text-gray-200">Bitvavo API • DeepSeek AI</div>
            </div>
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
          </div>
        </div>
      </div>
    </header>
  );
};

export default Header;