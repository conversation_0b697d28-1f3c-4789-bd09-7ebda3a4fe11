
import React from 'react';
import { Asset } from '../types';
import { TRADABLE_ASSET_SYMBOLS } from '../constants';

interface MarketIndicatorsProps {
  assets: Asset[];
}

const MarketIndicators: React.FC<MarketIndicatorsProps> = ({ assets }) => {
  const indicatorAssets = assets.filter(asset => 
      !TRADABLE_ASSET_SYMBOLS.includes(asset.symbol) && asset.price > 0
  );

  return (
    <div className="bg-gradient-to-br from-gray-800/70 to-gray-900/70 rounded-2xl p-5 border border-gray-700/30 backdrop-blur-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-semibold text-gray-200">Markt Indicatoren</h3>
        <span className="text-xs text-gray-500">{indicatorAssets.length} assets</span>
      </div>
      {/* Compact grid layout for better space usage */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
        {indicatorAssets.length > 0 ? indicatorAssets.map(asset => (
          <div key={asset.symbol} className="bg-gray-900/40 rounded-lg p-3 border border-gray-700/20 hover:bg-gray-900/60 transition-colors">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-white font-semibold text-sm">{asset.symbol.replace('-EUR', '')}</div>
                <div className="text-xs text-gray-400">{asset.name}</div>
              </div>
              <div className="text-right">
                <div className="font-mono text-white text-base font-bold">€{asset.price.toFixed(0)}</div>
                <div className="text-xs text-gray-400">prijs</div>
              </div>
            </div>
          </div>
        )) : (
          <div className="col-span-full text-center text-gray-500 py-6">
            <div className="text-sm">Geen markt indicatoren</div>
            <div className="text-xs text-gray-600 mt-1">Data wordt geladen...</div>
          </div>
        )}
      </div>
    </div>
  );
};

export default MarketIndicators;