
import { Asset } from './types';

export const INITIAL_BALANCE = 500;
export const TRADING_FEE_PERCENT = 0.0025; // Bitvavo fee is 0.25%

export const BITVAVO_TICKER_URL = 'https://api.bitvavo.com/v2/ticker/24h';
export const BITVAVO_CANDLES_BASE_URL = 'https://api.bitvavo.com/v2';

/* --- AI Model Configuration (Vite) ---
   Client-side env must be exposed with VITE_ prefix. Configure in .env.local.
*/
export const DEEPSEEK_API_KEY = (import.meta as any).env.VITE_DEEPSEEK_API_KEY as string || '';
// Always use direct API call since proxy has issues
export const DEEPSEEK_API_URL = 'https://api.deepseek.com/v1/chat/completions';




export const TRADABLE_ASSETS_CONFIG = [
  { symbol: 'XRP-EUR', name: 'Ripple' },
  { symbol: 'SOL-EUR', name: '<PERSON><PERSON>' },
  { symbol: 'DOGE-EUR', name: '<PERSON><PERSON><PERSON><PERSON>' },
];

export const TRADABLE_ASSET_SYMBOLS = TRADABLE_ASSETS_CONFIG.map(a => a.symbol);

const MARKET_INDICATORS: Asset[] = [
  { symbol: 'BTC-EUR', name: 'Bitcoin', price: 65000, priceChange24h: 0 },
  { symbol: 'ETH-EUR', name: 'Ethereum', price: 3500, priceChange24h: 0 },
];

export const INITIAL_ASSETS: Asset[] = [
  ...TRADABLE_ASSETS_CONFIG.map(a => ({ ...a, price: 0, priceChange24h: 0 })),
  ...MARKET_INDICATORS,
];