
import React from 'react';
import Price<PERSON>hart from './PriceChart';
import PredictionCard from './PredictionCard';
import { CandleDataPoint, Prediction, AppState } from '../types';
import { TrendingUp, Hourglass, AlertTriangle, CheckCircle, BrainCircuit } from 'lucide-react';

interface AssetDashboardCardProps {
  assetSymbol: string;
  candleHistory: CandleDataPoint[];
  prediction: Prediction | null;
  status: AppState;
  error: string | null;
  isDataSufficient: boolean;
}

const StateIndicator: React.FC<{ state: AppState; isDataSufficient: boolean }> = ({ state, isDataSufficient }) => {
  // Wachten op voldoende data (niet tijdens analyse of fout)
  if (!isDataSufficient && state !== AppState.Analyzing && state !== AppState.Error) {
    return (
      <span className="inline-flex items-center gap-1.5 rounded-full px-2 py-1 text-xs font-medium bg-yellow-500/15 text-yellow-400">
        <Hourglass size={16} className="animate-spin" />
        Data verzamelen...
      </span>
    );
  }

  switch (state) {
    case AppState.Initializing:
      return (
        <span className="inline-flex items-center gap-1.5 rounded-full px-2 py-1 text-xs font-medium bg-yellow-500/15 text-yellow-400">
          <Hourglass size={16} className="animate-spin" />
          Initialiseren...
        </span>
      );
    case AppState.Monitoring:
      return (
        <span className="inline-flex items-center gap-1.5 rounded-full px-2 py-1 text-xs font-medium bg-blue-500/15 text-blue-400">
          <TrendingUp size={16} />
          Monitoren...
        </span>
      );
    case AppState.Analyzing:
      return (
        <span className="inline-flex items-center gap-1.5 rounded-full px-2 py-1 text-xs font-medium bg-teal-500/15 text-teal-400">
          <BrainCircuit size={16} className="animate-pulse" />
          Analyse...
        </span>
      );
    case AppState.Error:
      return (
        <span className="inline-flex items-center gap-1.5 rounded-full px-2 py-1 text-xs font-medium bg-red-500/15 text-red-400">
          <AlertTriangle size={16} />
          Fout...
        </span>
      );
    default:
      return (
        <span className="inline-flex items-center gap-1.5 rounded-full px-2 py-1 text-xs font-medium bg-green-500/15 text-green-400">
          <CheckCircle size={16} />
          Stand-by
        </span>
      );
  }
};

const AssetDashboardCard: React.FC<AssetDashboardCardProps> = ({
  assetSymbol,
  candleHistory,
  prediction,
  status,
  error,
  isDataSufficient
}) => {
  return (
    <div className="bg-gray-800 p-5 md:p-6 rounded-xl shadow-2xl space-y-5 md:space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl md:text-3xl font-bold text-white tracking-tight">{assetSymbol.replace('-EUR', '')}</h2>
        <StateIndicator state={status} isDataSufficient={isDataSufficient} />
      </div>

      <PriceChart data={candleHistory} assetSymbol={assetSymbol} />

      <PredictionCard
        prediction={prediction}
        isLoading={status === AppState.Analyzing}
        error={error}
      />
    </div>
  );
};

export default AssetDashboardCard;